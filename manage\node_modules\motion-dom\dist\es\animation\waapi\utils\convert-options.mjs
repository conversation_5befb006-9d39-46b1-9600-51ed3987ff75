import { secondsToMilliseconds } from 'motion-utils';
import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';
import { createGeneratorEasing } from '../../generators/utils/create-generator-easing.mjs';
import { isGenerator } from '../../generators/utils/is-generator.mjs';
import { mapEasingToNativeEasing } from './easing.mjs';

const defaultEasing = "easeOut";
function applyGeneratorOptions(options) {
    var _a;
    if (isGenerator(options.type)) {
        const generatorOptions = createGeneratorEasing(options, 100, options.type);
        options.ease = supportsLinearEasing()
            ? generatorOptions.ease
            : defaultEasing;
        options.duration = secondsToMilliseconds(generatorOptions.duration);
        options.type = "keyframes";
    }
    else {
        options.duration = secondsToMilliseconds((_a = options.duration) !== null && _a !== void 0 ? _a : 0.3);
        options.ease = options.ease || defaultEasing;
    }
}
// TODO: Reuse for NativeAnimation
function convertMotionOptionsToNative(valueName, keyframes, options) {
    var _a;
    const nativeKeyframes = {};
    const nativeOptions = {
        fill: "both",
        easing: "linear",
        composite: "replace",
    };
    nativeOptions.delay = secondsToMilliseconds((_a = options.delay) !== null && _a !== void 0 ? _a : 0);
    applyGeneratorOptions(options);
    nativeOptions.duration = options.duration;
    const { ease, times } = options;
    if (times)
        nativeKeyframes.offset = times;
    nativeKeyframes[valueName] = keyframes;
    const easing = mapEasingToNativeEasing(ease, options.duration);
    /**
     * If this is an easing array, apply to keyframes, not animation as a whole
     */
    if (Array.isArray(easing)) {
        nativeKeyframes.easing = easing;
    }
    else {
        nativeOptions.easing = easing;
    }
    return {
        keyframes: nativeKeyframes,
        options: nativeOptions,
    };
}

export { applyGeneratorOptions, convertMotionOptionsToNative };
