<template>
  <div class="app-wrapper">
    <el-container>
      <el-aside width="200px">
        <div class="logo">医院膳食系统</div>
        <el-menu
          router
          :default-active="$route.path"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-submenu index="1">
            <template #title>
              <i class="el-icon-user"></i>
              <span>用户管理</span>
            </template>
            <el-menu-item index="/user">用户列表</el-menu-item>
            <el-menu-item index="/role">角色管理</el-menu-item>
          </el-submenu>
          <el-submenu index="2">
            <template #title>
              <i class="el-icon-menu"></i>
              <span>菜单管理</span>
            </template>
            <el-menu-item index="/menu">菜单配置</el-menu-item>
          </el-submenu>
          <el-submenu index="3">
            <template #title>
              <i class="el-icon-notebook-2"></i>
              <span>膳食字典</span>
            </template>
            <el-menu-item index="/food">膳食列表</el-menu-item>
            <el-menu-item index="/food-category">分类管理</el-menu-item>
          </el-submenu>
          <el-submenu index="4">
            <template #title>
              <i class="el-icon-date"></i>
              <span>食谱管理</span>
            </template>
            <el-menu-item index="/recipe">食谱排期</el-menu-item>
            <el-menu-item index="/recipe-plan">食谱模板</el-menu-item>
          </el-submenu>
        </el-menu>
      </el-aside>
      <el-container>
        <el-header>
          <div class="header-right">
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-avatar :size="30" src="https://nocode.meituan.com/photo/search?keyword=doctor&width=100&height=100" />
                <span class="username">管理员</span>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人中心</el-dropdown-item>
                  <el-dropdown-item>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref } from 'vue'
</script>

<style scoped>
.app-wrapper {
  height: 100vh;
}
.logo {
  height: 60px;
  line-height: 60px;
  text-align: center;
  color: #fff;
  background-color: #2b2f3a;
  font-size: 18px;
  font-weight: bold;
}
.el-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.header-right {
  display: flex;
  align-items: center;
}
.username {
  margin-left: 10px;
}
.el-aside {
  background-color: #304156;
}
.el-menu {
  border-right: none;
}
</style>
