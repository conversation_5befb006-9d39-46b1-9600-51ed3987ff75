import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ChevronRight, ArrowLeft } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import AuthGuard from '../components/AuthGuard';
import { useAuth } from '../hooks/useAuth';

const Patients = () => {
  const { wardId } = useParams();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const { handleAuthError } = useAuth();

  const { data: wardInfo = {}, isLoading: isLoadingWard, error: wardError } = useQuery({
    queryKey: ['ward', wardId],
    queryFn: async () => {
      console.log('获取病区详情, wardId:', wardId);
      const token = localStorage.getItem('token');

      const response = await fetch(`/api/wards/${wardId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('病区详情响应:', response.status, response.statusText);

      if (!response.ok) {
        if (handleAuthError(response)) {
          return {};
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '获取病区详情失败');
      }

      const data = await response.json();
      console.log('病区详情数据:', data);
      return data;
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  const { data: patients = [], isLoading, error: patientsError } = useQuery({
    queryKey: ['patients', wardId],
    queryFn: async () => {
      console.log('获取病人列表, wardId:', wardId);
      const token = localStorage.getItem('token');

      const response = await fetch(`/api/wards/${wardId}/patients`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('病人列表响应:', response.status, response.statusText);

      if (!response.ok) {
        if (handleAuthError(response)) {
          return [];
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '获取病人列表失败');
      }

      const data = await response.json();
      console.log('病人列表数据:', data);
      return data;
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.bedNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoadingWard || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (wardError || patientsError) {
    const error = wardError || patientsError;
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <div className="space-x-4">
            <button
              onClick={() => navigate('/wards')}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              返回病区列表
            </button>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white p-4 border-b sticky top-0 z-10">
          <div className="flex justify-between items-center mb-4">
            <button onClick={() => navigate('/wards')} className="text-blue-500">
              <ArrowLeft className="h-6 w-6" />
            </button>
            <div className="text-center">
              <h1 className="text-xl font-bold">{wardInfo?.name || '病区'}</h1>
              <p className="text-sm text-gray-600">主治医生: {wardInfo?.doctor || '未知'}</p>
            </div>

        </div>

        <div className="relative">
          <input
            type="text"
            placeholder="搜索病人..."
            className="w-full p-2 pl-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute left-3 top-2.5 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-3">
        {filteredPatients.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? '没有找到匹配的病人' : '暂无病人数据'}
          </div>
        ) : (
          filteredPatients.map(patient => (
            <div
              key={patient.id}
              onClick={() => navigate(`/?patientId=${patient.id}`)}
              className="bg-white rounded-lg shadow p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-bold text-lg">{patient.name}</h3>
                  <p className="text-sm text-gray-600">{patient.bedNumber} | {patient.age}岁 | {patient.gender}</p>
                  <p className="text-sm text-gray-600">诊断: {patient.diagnosis}</p>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400" />
              </div>
            </div>
          ))
        )}
      </div>
    </div>
    </AuthGuard>
  );
};

export default Patients;
