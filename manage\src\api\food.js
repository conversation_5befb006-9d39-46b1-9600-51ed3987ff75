import request from '@/utils/request'
import Mock from 'mockjs'

// Mock数据
Mock.mock('/api/food/list', 'post', (options) => {
  const { pageNum, pageSize, name, category } = JSON.parse(options.body)
  const total = 35
  const list = []
  
  for (let i = 0; i < pageSize; i++) {
    const index = (pageNum - 1) * pageSize + i
    if (index >= total) break
    
    list.push({
      id: 'F' + Mock.mock('@natural(1000,9999)'),
      name: Mock.mock('@ctitle(3,5)') + '餐',
      category: Mock.mock('@pick(["普通膳食","治疗膳食","特殊膳食"])'),
      price: Mock.mock('@float(10,50,2,2)'),
      sort: Mock.mock('@integer(1,100)'),
      image: 'https://nocode.meituan.com/photo/search?keyword=food&width=200&height=200'
    })
  }
  
  return {
    code: 200,
    data: {
      list,
      total,
      pageNum,
      pageSize
    }
  }
})

Mock.mock('/api/food/add', 'post', { code: 200, message: '添加成功' })
Mock.mock('/api/food/update', 'post', { code: 200, message: '修改成功' })
Mock.mock('/api/food/delete', 'post', { code: 200, message: '删除成功' })

// API方法
export function getFoodList(params) {
  return request({
    url: '/api/food/list',
    method: 'post',
    data: params
  })
}

export function addFood(data) {
  return request({
    url: '/api/food/add',
    method: 'post',
    data
  })
}

export function updateFood(data) {
  return request({
    url: '/api/food/update',
    method: 'post',
    data
  })
}

export function deleteFood(id) {
  return request({
    url: '/api/food/delete',
    method: 'post',
    data: { id }
  })
}
