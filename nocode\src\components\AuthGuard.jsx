import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const AuthGuard = ({ children }) => {
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      // 如果没有 token，直接跳转到登录页面
      navigate('/login', { replace: true });
    }
  }, [navigate]);

  // 如果没有 token，不渲染子组件
  const token = localStorage.getItem('token');
  if (!token) {
    return null;
  }

  return children;
};

export default AuthGuard;
