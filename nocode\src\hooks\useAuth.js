import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

export const useAuth = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      // 如果没有 token，直接跳转到登录页面
      navigate('/login', { replace: true });
    }
  }, [navigate]);

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login', { replace: true });
      return false;
    }
    return true;
  };

  const handleAuthError = (response) => {
    if (response && response.status === 401) {
      localStorage.removeItem('token');
      navigate('/login', { replace: true });
      return true;
    }
    return false;
  };

  return { checkAuth, handleAuthError };
};
