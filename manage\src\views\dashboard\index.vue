<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="6" v-for="item in cardData" :key="item.title">
        <el-card shadow="hover">
          <div class="card-content">
            <div class="card-icon" :style="{ backgroundColor: item.color }">
              <component :is="item.icon" style="font-size: 24px" />
            </div>
            <div class="card-text">
              <div class="card-title">{{ item.title }}</div>
              <div class="card-value">{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-card shadow="never" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>本周食谱统计</span>
        </div>
      </template>
      <div id="chart" style="height: 300px"></div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import {
  User,
  Notebook,
  ShoppingCart,
  Money
} from '@element-plus/icons-vue'

const cardData = ref([
  {
    title: '用户数量',
    value: '128',
    icon: User,
    color: '#409EFF'
  },
  {
    title: '膳食种类',
    value: '56',
    icon: Notebook,
    color: '#67C23A'
  },
  {
    title: '今日订单',
    value: '342',
    icon: ShoppingCart,
    color: '#E6A23C'
  },
  {
    title: '本月收入',
    value: '¥28,560',
    icon: Money,
    color: '#F56C6C'
  }
])

onMounted(() => {
  initChart()
})

const initChart = () => {
  const chart = echarts.init(document.getElementById('chart'))
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['早餐', '午餐', '晚餐']
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '早餐',
        type: 'bar',
        data: [120, 132, 101, 134, 90, 230, 210],
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '午餐',
        type: 'bar',
        data: [220, 182, 191, 234, 290, 330, 310],
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '晚餐',
        type: 'bar',
        data: [150, 232, 201, 154, 190, 330, 410],
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  }
  chart.setOption(option)
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}
.card-content {
  display: flex;
  align-items: center;
}
.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 15px;
}
.card-text {
  flex: 1;
}
.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}
.card-value {
  font-size: 22px;
  font-weight: bold;
}
</style>
