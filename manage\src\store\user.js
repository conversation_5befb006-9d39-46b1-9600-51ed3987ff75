import { defineStore } from 'pinia'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    name: '',
    avatar: '',
    roles: []
  }),
  actions: {
    login(userInfo) {
      return new Promise((resolve) => {
        this.token = 'mock-token'
        setToken(this.token)
        resolve()
      })
    },
    logout() {
      return new Promise((resolve) => {
        this.token = ''
        removeToken()
        resolve()
      })
    }
  }
})
