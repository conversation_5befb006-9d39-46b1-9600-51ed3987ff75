import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'
import { getToken } from '@/utils/auth'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页' }
      },
      {
        path: 'user',
        component: () => import('@/views/user/index.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'role',
        component: () => import('@/views/role/index.vue'),
        meta: { title: '角色管理' }
      },
      {
        path: 'menu',
        component: () => import('@/views/menu/index.vue'),
        meta: { title: '菜单管理' }
      },
      {
        path: 'food',
        component: () => import('@/views/food/index.vue'),
        meta: { title: '膳食字典' }
      },
      {
        path: 'food-category',
        component: () => import('@/views/food-category/index.vue'),
        meta: { title: '分类管理' }
      },
      {
        path: 'recipe',
        component: () => import('@/views/recipe/index.vue'),
        meta: { title: '食谱排期' }
      },
      {
        path: 'recipe-plan',
        component: () => import('@/views/recipe-plan/index.vue'),
        meta: { title: '食谱模板' }
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  const hasToken = getToken()
  
  if (to.path === '/login') {
    if (hasToken) {
      next({ path: '/' })
    } else {
      next()
    }
  } else {
    if (!hasToken) {
      next(`/login?redirect=${to.path}`)
    } else {
      next()
    }
  }
})

export default router
