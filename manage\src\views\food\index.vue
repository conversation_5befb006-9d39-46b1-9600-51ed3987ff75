<template>
  <div class="food-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>膳食字典管理</span>
          <el-button type="primary" @click="handleAdd">新增膳食</el-button>
        </div>
      </template>
      
      <el-form :inline="true" :model="queryParams">
        <el-form-item label="膳食名称">
          <el-input v-model="queryParams.name" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="膳食分类">
          <el-select v-model="queryParams.category" placeholder="请选择" clearable>
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="foodList" border style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="膳食名称" width="120" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{row}">
            <el-tag :type="getCategoryTagType(row.category)">
              {{ row.category }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价(元)" width="100" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column label="图片" width="120">
          <template #default="{row}">
            <el-image
              style="width: 80px; height: 80px"
              :src="row.image || 'https://nocode.meituan.com/photo/search?keyword=food&width=200&height=200'"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="{row}">
            <el-button size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="50%">
      <el-form ref="foodForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="膳食名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入膳食名称" />
        </el-form-item>
        <el-form-item label="膳食分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单价(元)" prop="price">
          <el-input-number v-model="form.price" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="排序号" prop="sort">
          <el-input-number v-model="form.sort" :min="1" />
        </el-form-item>
        <el-form-item label="膳食图片" prop="image">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :on-change="handleImageChange"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { getFoodList, addFood, updateFood, deleteFood } from '@/api/food'

const foodList = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const categoryOptions = [
  { value: '普通膳食', label: '普通膳食' },
  { value: '治疗膳食', label: '治疗膳食' },
  { value: '特殊膳食', label: '特殊膳食' }
]

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  category: ''
})

const form = reactive({
  id: '',
  name: '',
  category: '',
  price: 0,
  sort: 1,
  image: ''
})

const rules = reactive({
  name: [{ required: true, message: '请输入膳食名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }]
})

const getCategoryTagType = (category) => {
  const map = {
    '普通膳食': '',
    '治疗膳食': 'success',
    '特殊膳食': 'warning'
  }
  return map[category] || ''
}

const handleQuery = async () => {
  const res = await getFoodList(queryParams)
  foodList.value = res.data.list
  total.value = res.data.total
}

const resetQuery = () => {
  queryParams.pageNum = 1
  queryParams.name = ''
  queryParams.category = ''
  handleQuery()
}

const handleSizeChange = (val) => {
  queryParams.pageSize = val
  handleQuery()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  handleQuery()
}

const handleAdd = () => {
  dialogTitle.value = '新增膳食'
  Object.assign(form, {
    id: '',
    name: '',
    category: '',
    price: 0,
    sort: 1,
    image: ''
  })
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑膳食'
  Object.assign(form, JSON.parse(JSON.stringify(row)))
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该膳食吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deleteFood(row.id)
    ElMessage.success('删除成功')
    handleQuery()
  } catch {
    // 用户取消删除
  }
}

const handleImageChange = (file) => {
  form.image = URL.createObjectURL(file.raw)
}

const submitForm = async () => {
  try {
    if (form.id) {
      await updateFood(form)
      ElMessage.success('修改成功')
    } else {
      await addFood(form)
      ElMessage.success('新增成功')
    }
    dialogVisible.value = false
    handleQuery()
  } catch (err) {
    console.error(err)
  }
}

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
